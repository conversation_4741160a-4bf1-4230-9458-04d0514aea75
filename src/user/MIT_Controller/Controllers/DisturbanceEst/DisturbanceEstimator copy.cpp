#include "DisturbanceEstimator.h"


DisturbanceEstimator::DisturbanceEstimator(float _dt, int _iterations_between_mpc, MIT_UserParameters* parameters) {
    dt =_dt;
    iterations_between_mpc = _iterations_between_mpc;
    dtMPC = dt * iterations_between_mpc;
    _parameters = parameters;
}

void DisturbanceEstimator::initialize() {
    Eigen::Matrix<float,3,1> Id;
    Id << 1.58460467e-01, 4.68645637e-01, 5.24474661e-01;
    I_body.diagonal() = Id;
    K << 1.0, 1.0, 1.0;
    mp = 0.0f;
    disturbance.setZero();
    foot_force_tau_est.setZero();
    foot_force_fused.setZero();
}

void DisturbanceEstimator::run(ControlFSMData<float>& data) {
    body_mass = 14;
    auto& estResult = data._stateEstimator->getResult();
    foot_force_est = estResult.footForceEstimate;
    contact_state = estResult.contactEstimate;
    acc_world = estResult.aWorld[2] - 9.81f;  // 直接计算加速度偏差，取消非负限制

    // 基于关节扭矩传感器观测值估计足端接触力
    estimateFootForceFromTorque(data);

    // 融合两种估计方法的结果
    fuseForceEstimates();

    // 统计接触状态中零的个数（假设contact_state[i]为0表示不接触）
    int zero_count = 0;
    for (int i = 0; i < 4; i++) {
        if (contact_state[i] > 0.1f) {
            zero_count++;
        }
    }

    // 只有当恰好两条腿接触（即两个零）时才更新
    if (zero_count == 2) {
        total_contact_force = 0.0f;
        for (int i = 0; i < 4; i++) {
            if (contact_state[i] != 0.0f) {
                total_contact_force += foot_force_est[i];
            }
        }

        // 避免除以零：当加速度接近 -9.81 时，分母很小
        if (fabs(acc_world + 9.81f) > 1e-3f) {
            float total_mass = total_contact_force / (9.81f + acc_world);
            float mp_new = total_mass - body_mass;

            // 低通滤波，系数alpha可调整（0.1f表示较慢响应，更平滑）
            float alpha = 0.1f;
            mp = alpha * mp_new + (1 - alpha) * mp;
        }
    }

    // 调试输出（每500次输出一次）
    static int counter = 0;
    if (counter++ >= 500) {
        counter = 0;
        for (int i = 0; i < 4; i++) {
            std::cout << "foot_force_est[" << i << "]: " << foot_force_est[i] << std::endl;
            std::cout << "contact_state[" << i << "]: " << contact_state[i] << std::endl;
        }
        std::cout << "total_contact_force: " << total_contact_force << std::endl;
        std::cout << "acc_world: " << acc_world << std::endl;
        std::cout << "mp: " << mp << std::endl;
    }
}

void DisturbanceEstimator::estimateFootForceFromTorque(ControlFSMData<float>& data) {
    // 检查是否启用扭矩估计
    if (!enable_torque_estimation) {
        foot_force_tau_est[2] = foot_force_est; // 如果未启用，直接复制原有估计
        return;
    }

    // 获取关节扭矩传感器数据和雅可比矩阵
    for (int leg = 0; leg < 4; leg++) {
        // 获取当前腿的关节扭矩观测值
        Vec3<float> tau_est = data._legController->datas[leg].tauEstimate;

        // 获取当前腿的关节位置和速度
        Vec3<float> q = data._legController->datas[leg].q;
        Vec3<float> qd = data._legController->datas[leg].qd;

        // 获取当前腿的雅可比矩阵（已在LegController中计算）
        Mat3<float> J = data._legController->datas[leg].J;

        // 计算重力补偿扭矩（简化模型）
        Vec3<float> tau_gravity = Vec3<float>::Zero();
        if (enable_gravity_compensation) {
            tau_gravity = computeGravityCompensation(q, leg, data);
        }

        // 计算惯性补偿扭矩（简化模型）
        Vec3<float> tau_inertia = Vec3<float>::Zero();
        if (enable_inertia_compensation) {
            tau_inertia = computeInertiaCompensation(q, qd, leg, data);
        }

        // 从观测扭矩中减去重力和惯性项，得到接触力产生的扭矩
        Vec3<float> tau_contact = tau_est - tau_gravity - tau_inertia;

        // 通过逆向动力学计算足端接触力
        // 公式: tau_contact = J^T * F_foot
        // 求解: F_foot = (J^T)^(-1) * tau_contact

        // 计算雅可比矩阵转置的伪逆
        Mat3<float> J_transpose = J.transpose();
        Mat3<float> J_transpose_pinv;

        // 使用SVD计算伪逆矩阵
        Eigen::JacobiSVD<Mat3<float>> svd(J_transpose, Eigen::ComputeFullU | Eigen::ComputeFullV);
        float tolerance = 1e-6f;

        // 计算伪逆
        auto singular_values = svd.singularValues();
        Mat3<float> sigma_inv = Mat3<float>::Zero();
        for (int i = 0; i < 3; i++) {
            if (singular_values(i) > tolerance) {
                sigma_inv(i, i) = 1.0f / singular_values(i);
            }
        }
        J_transpose_pinv = svd.matrixV() * sigma_inv * svd.matrixU().transpose();

        // 计算足端接触力
        Vec3<float> foot_force = -J_transpose_pinv * tau_contact;
        foot_force_tau_est[leg] = foot_force;
        // 简单的低通滤波
        static Vec3<float> foot_force_tau_est_prev[4] = {Vec3<float>::Zero(), Vec3<float>::Zero(), Vec3<float>::Zero(), Vec3<float>::Zero()};
        foot_force_tau_est[leg] = torque_filter_alpha * foot_force_tau_est_prev[leg] +
                                  (1.0f - torque_filter_alpha) * foot_force_tau_est[leg];
        foot_force_tau_est_prev[leg] = foot_force_tau_est[leg];
    }

    // 调试输出（每1000次输出一次）
    static int tau_counter = 0;
    if (tau_counter++ >= 1000) {
        tau_counter = 0;
        std::cout << "=== Torque-based Force Estimation ===" << std::endl;
        for (int i = 0; i < 4; i++) {
            std::cout << "Leg " << i << " - tau_est: ["
                      << data._legController->datas[i].tauEstimate[0] << ", "
                      << data._legController->datas[i].tauEstimate[1] << ", "
                      << data._legController->datas[i].tauEstimate[2] << "]" << std::endl;
            std::cout << "Leg " << i << " - foot_force_tau_est: "
                      << foot_force_tau_est[i] << " N" << std::endl;
            std::cout << "Leg " << i << " - foot_force_est (original): "
                      << foot_force_est[i] << " N" << std::endl;
            std::cout << "Leg " << i << " - foot_force_fused: "
                      << foot_force_fused[i] << " N" << std::endl;
            std::cout << "Leg " << i << " - contact_state: "
                      << contact_state[i] << std::endl;
        }
        std::cout << "Total force (tau-based): "
                  << (foot_force_tau_est[0] + foot_force_tau_est[1] +
                      foot_force_tau_est[2] + foot_force_tau_est[3]) << " N" << std::endl;
        std::cout << "========================================" << std::endl;
    }
}

Vec3<float> DisturbanceEstimator::computeGravityCompensation(const Vec3<float>& q, int leg, ControlFSMData<float>& data) {
    // 简化的重力补偿模型
    // 基于腿部连杆质量和重力的影响

    // 腿部连杆参数（简化模型）
    float m1 = 0.54f;  // abad连杆质量 (kg)
    float m2 = 0.634f; // hip连杆质量 (kg)
    float m3 = 0.064f; // knee连杆质量 (kg)

    float l1 = data._quadruped->_abadLinkLength;
    float l2 = data._quadruped->_hipLinkLength;
    float l3 = data._quadruped->_kneeLinkLength;

    // 重心位置（简化为连杆中点）
    float lc1 = l1 * 0.5f;
    float lc2 = l2 * 0.5f;
    float lc3 = l3 * 0.5f;

    float g = 9.81f;

    // 关节角度
    float q1 = q[0]; // abad
    float q2 = q[1]; // hip
    float q3 = q[2]; // knee

    // 计算重力扭矩（简化模型）
    Vec3<float> tau_gravity;

    // abad关节重力扭矩
    tau_gravity[0] = 0.0f; // abad轴通常垂直，重力扭矩较小

    // hip关节重力扭矩
    tau_gravity[1] = g * (m2 * lc2 * cos(q2) + m3 * (l2 * cos(q2) + lc3 * cos(q2 + q3)));

    // knee关节重力扭矩
    tau_gravity[2] = g * m3 * lc3 * cos(q2 + q3);

    return tau_gravity;
}

Vec3<float> DisturbanceEstimator::computeInertiaCompensation(const Vec3<float>& q, const Vec3<float>& qd, int leg, ControlFSMData<float>& data) {
    // 简化的惯性补偿模型
    // 基于腿部连杆惯性和科里奥利力的影响

    // 腿部连杆参数（简化模型）
    float m1 = 0.54f;  // abad连杆质量 (kg)
    float m2 = 0.634f; // hip连杆质量 (kg)
    float m3 = 0.064f; // knee连杆质量 (kg)

    float l1 = data._quadruped->_abadLinkLength;
    float l2 = data._quadruped->_hipLinkLength;
    float l3 = data._quadruped->_kneeLinkLength;

    // 惯性参数（简化）
    float I1 = m1 * l1 * l1 / 12.0f;
    float I2 = m2 * l2 * l2 / 12.0f;
    float I3 = m3 * l3 * l3 / 12.0f;

    // 关节角度和角速度
    float q1 = q[0], q2 = q[1], q3 = q[2];
    float qd1 = qd[0], qd2 = qd[1], qd3 = qd[2];

    // 简化的惯性扭矩计算（主要考虑科里奥利力和离心力）
    Vec3<float> tau_inertia;

    // 简化模型：主要考虑关节间的耦合效应
    tau_inertia[0] = I1 * 0.1f * (qd2 * qd2 + qd3 * qd3); // abad轴受其他轴影响
    tau_inertia[1] = I2 * 0.2f * qd1 * qd3 * sin(q3);     // hip轴科里奥利力
    tau_inertia[2] = I3 * 0.1f * qd1 * qd2 * cos(q2);     // knee轴科里奥利力

    return tau_inertia;
}

void DisturbanceEstimator::fuseForceEstimates() {
    // 融合基于扭矩的估计和原有的估计方法
    for (int leg = 0; leg < 4; leg++) {
        // 根据接触状态调整融合权重
        float contact_confidence = contact_state[leg];

        // 当接触置信度高时，更信任扭矩估计
        // 当接触置信度低时，更信任原有估计
        float tau_weight = fusion_base_weight + 0.4f * contact_confidence; // 基础权重 + 动态调整
        float original_weight = 1.0f - tau_weight;

        // 对扭矩估计进行可信度检查
        float tau_reliability = 1.0f;
        if (foot_force_tau_est[leg][2] > 300.0f) { // 如果扭矩估计过大，降低可信度
            tau_reliability = 0.5f;
        }
        if (foot_force_tau_est[leg][2] < 5.0f && contact_state[leg] > 0.5f) { // 如果应该接触但扭矩估计很小
            tau_reliability = 0.3f;
        }

        tau_weight *= tau_reliability;
        original_weight = 1.0f - tau_weight;

        // 执行加权融合
        foot_force_fused[leg] = tau_weight * foot_force_tau_est[leg][2] +
                               original_weight * foot_force_est[leg];

        // 确保融合结果合理
        if (foot_force_fused[leg] < 0.0f) {
            foot_force_fused[leg] = 0.0f;
        }
        if (foot_force_fused[leg] > 400.0f) {
            foot_force_fused[leg] = 400.0f;
        }
    }
}