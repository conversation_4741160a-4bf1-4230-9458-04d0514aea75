#include "DisturbanceEstimator.h"

DisturbanceEstimator::DisturbanceEstimator(float _dt, int _iterations_between_mpc, MIT_UserParameters* parameters) {
    dt =_dt;
    iterations_between_mpc = _iterations_between_mpc;
    dtMPC = dt * iterations_between_mpc;
    _parameters = parameters;
}

void DisturbanceEstimator::initialize() {
    Eigen::Matrix<float,3,1> Id;
    Id << 1.58460467e-01, 4.68645637e-01, 5.24474661e-01;
    I_body.diagonal() = Id;
    K << 1.0, 1.0, 1.0;
    mp = 0.0f; //基于力传感器估计的质量
    mp_torque_based = 0.0f;  // 新增：基于扭矩估计的质量
    disturbance.setZero();
    for(int i = 0; i < 4; i++) {
    foot_force_tau_est[i].setZero();
    }
}

void DisturbanceEstimator::run(ControlFSMData<float>& data) {
    body_mass = 14;
    auto& estResult = data._stateEstimator->getResult();
    foot_force_est = estResult.footForceEstimate;
    contact_state = estResult.contactEstimate;
    // 获取完整的三维加速度信息
    acc_world_3d = estResult.aWorld;
    Vec3<float> gravity(0, 0, -9.81f); // 重力矢量
    // 获取机器人姿态
    Mat3<float> R = estResult.rBody;

    // 基于关节扭矩传感器观测值估计足端接触力
    estimateFootForceFromTorque(data);

    // 统计接触状态中零的个数（假设contact_state[i]为0表示不接触）
    int contact_count = 0;
    for (int i = 0; i < 4; i++) {
        if (contact_state[i] > 0.1f) {
            contact_count++;
        }
    }

    // 只有当恰好两条腿接触时才更新
    if (contact_count == 4) {
        // 方法2: 基于扭矩估计的三维力估计负载质量
        // 获取机体到世界坐标系的旋转矩阵
        Mat3<float> R = estResult.rBody.transpose(); // 世界到机体的转置 = 机体到世界

        // 简化版本：先只使用Z分量（垂直力）
        float total_force_z_tau = 0.0f;
        total_contact_force_tau_est = Vec3<float>::Zero();

        for (int i = 0; i < 4; i++) {
            if (contact_state[i] > 0.1f) {
                // 只使用Z分量（垂直方向的接触力）
                float force_z = fabs(foot_force_tau_est[i][2]); // 取绝对值确保为正
                total_force_z_tau += force_z;

                // 暂时假设垂直方向就是世界坐标系的Z方向
                total_contact_force_tau_est[2] += force_z;
            }
        }

        if ((acc_world_3d - gravity).norm() > 1e-3f) {
            // 如果接触力太小，使用理论最小值
            if (total_contact_force < 50.0f) {
                total_contact_force = body_mass * 9.81f; // 使用机器人重量作为最小接触力
                std::cout << "Warning: Contact force too small, using theoretical minimum: "
                          << total_contact_force << " N" << std::endl;
            }
            // 方法1: 基于原有foot_force_est估计负载质量（只使用垂直分量）
            float total_contact_force = 0.0f;
            for (int i = 0; i < 4; i++) {
                if (contact_state[i] > 0.1f) {
                    total_contact_force += foot_force_est[i];
                }
            }
            
            // 计算两种方法的质量估计
            // 方法1: 基于原有foot_force_est（只使用垂直分量）
            float denominator = -9.81f + acc_world_3d[2];
            if (fabs(denominator) < 0.1f) { // 避免除以接近零的数
                denominator = 9.81f; // 使用重力加速度作为默认值
                total_mass_original = total_contact_force / denominator;
            } else {
                total_mass_original = total_contact_force / acc_world_3d[2];
            }
            float mp_new_original = total_mass_original - body_mass;

            // 合理性检查：负载质量应该在合理范围内
            if (mp_new_original < -5.0f) mp_new_original = -5.0f;  // 最小负载（可能是传感器误差）
            if (mp_new_original > 20.0f) mp_new_original = 20.0f;    // 最大负载限制

            // 方法2: 基于扭矩估计的垂直力（简化版本）
            // 只使用垂直方向的力和加速度
            float total_mass_torque = total_force_z_tau / (acc_world_3d[2]);
            float mp_new_torque = total_mass_torque - body_mass;

            // 合理性检查：扭矩估计的负载质量
            if (mp_new_torque < -10.0f) mp_new_torque = -10.0f;
            if (mp_new_torque > 20.0f) mp_new_torque = 20.0f;

            // 低通滤波
            float alpha = 0.1f;
            mp = alpha * mp_new_original + (1 - alpha) * mp;
            mp_torque_based = alpha * mp_new_torque + (1 - alpha) * mp_torque_based;
            
            // 在质量层面进行融合
            // 根据接触状态和质量估计的合理性分配权重
            
            // 计算扭矩估计的置信度
            torque_confidence = 1.0f;
            
            // 检查力的方向是否合理（应该大致向上）
            Vec3<float> avg_force_direction = total_contact_force_tau_est.normalized();
            float vertical_alignment = fabs(avg_force_direction[2]); // Z分量应该接近1
            
            // 检查力的幅值是否合理
            float force_magnitude = total_contact_force_tau_est.norm();
            float expected_force = body_mass * 9.81f; // 预期的大致力值
            
            // 根据对齐程度和力的大小调整置信度
            if (vertical_alignment < 0.8f) {
                torque_confidence *= 0.5f; // 力方向不够垂直，降低置信度
            }
            
            if (fabs(force_magnitude - expected_force) > 0.5f * expected_force) {
                torque_confidence *= 0.5f; // 力大小不合理，降低置信度
            }
            
            // 根据置信度分配权重
            float weight_torque = 0.5f * torque_confidence;
            float weight_original = 1.0f - weight_torque;
            
            // 如果扭矩估计的质量明显不合理，进一步降低其权重
            if (mp_new_torque < -5.0f || mp_new_torque > 30.0f) {
                weight_torque *= 0.4f;
                weight_original = 1.0f - weight_torque;
            }
            
            // 融合两种质量估计
            mp_fused = weight_original * mp + weight_torque * mp_torque_based;
            
            // 限制融合后的质量在合理范围内
            if (mp_fused < -5.0f) mp_fused = -5.0f;
            if (mp_fused > 20.0f) mp_fused = 20.0f;
        }
    }

    // 调试输出（每500次输出一次）
    static int counter = 0;
    if (counter++ >= 500) {
        counter = 0;

        // 添加详细的调试信息
        std::cout << "=== Detailed Torque Analysis ===" << std::endl;
        for (int i = 0; i < 4; i++) {
            if (contact_state[i] > 0.1f) {
                std::cout << "Leg " << i << " (in contact):" << std::endl;
                std::cout << "  tau_est: ["
                          << data._legController->datas[i].tauEstimate[0] << ", "
                          << data._legController->datas[i].tauEstimate[1] << ", "
                          << data._legController->datas[i].tauEstimate[2] << "]" << std::endl;

                // 计算重力补偿
                Vec3<float> q = data._legController->datas[i].q;
                Vec3<float> tau_gravity = computeGravityCompensation(q, i, data);
                std::cout << "  tau_gravity: ["
                          << tau_gravity[0] << ", " << tau_gravity[1] << ", " << tau_gravity[2] << "]" << std::endl;

                Vec3<float> tau_contact = data._legController->datas[i].tauEstimate - tau_gravity;
                std::cout << "  tau_contact: ["
                          << tau_contact[0] << ", " << tau_contact[1] << ", " << tau_contact[2] << "]" << std::endl;

                std::cout << "  foot_force_tau_est: ["
                          << foot_force_tau_est[i][0] << ", " << foot_force_tau_est[i][1] << ", "
                          << foot_force_tau_est[i][2] << "]" << std::endl;
                std::cout << "foot_force_est: ["
                          << foot_force_est[i] << "]" << std::endl;
            }
        }
        std::cout << "=== Disturbance Estimator (3D) ===" << std::endl;
        std::cout << "Contact count: " << contact_count << std::endl;
        std::cout << "Total force (torque-based, 3D): " << total_contact_force_tau_est.norm() << " N" << std::endl;
        std::cout << "Total force vector (torque-based): [" << total_contact_force_tau_est[0]
                  << ", " << total_contact_force_tau_est[1] << ", " << total_contact_force_tau_est[2] << "]" << std::endl;
        std::cout << "Total force (original): " << total_contact_force << " N" << std::endl;
        std::cout << "Acceleration (3D): " << acc_world_3d.transpose() << " m/s^2" << std::endl;
        std::cout << "MP (original): " << mp << " kg" << std::endl;
        std::cout << "MP (torque-based, 3D): " << mp_torque_based << " kg" << std::endl;
        std::cout << "MP (fused): " << mp_fused << " kg" << std::endl;
        std::cout << "Torque confidence: " << torque_confidence << std::endl;
    }
}

void DisturbanceEstimator::estimateFootForceFromTorque(ControlFSMData<float>& data) {
    // 检查是否启用扭矩估计
    if (!enable_torque_estimation) {
        for (int i = 0; i < 4; i++) {
            foot_force_tau_est[i].setZero();
            foot_force_tau_est[i][2] = foot_force_est[i]; // 如果未启用，直接复制原有估计
        }
        return;
    }

    // 获取关节扭矩传感器数据和雅可比矩阵
    for (int leg = 0; leg < 4; leg++) {
        // 获取当前腿的关节扭矩观测值
        Vec3<float> tau_est = data._legController->datas[leg].tauEstimate;

        // 获取当前腿的关节位置和速度
        Vec3<float> q = data._legController->datas[leg].q;
        Vec3<float> qd = data._legController->datas[leg].qd;

        // 获取当前腿的雅可比矩阵（已在LegController中计算）
        Mat3<float> J = data._legController->datas[leg].J;

        // 计算重力补偿扭矩（简化模型）
        Vec3<float> tau_gravity = Vec3<float>::Zero();
        if (enable_gravity_compensation) {
            tau_gravity = computeGravityCompensation(q, leg, data);
        }

        // 计算惯性补偿扭矩（简化模型）
        Vec3<float> tau_inertia = Vec3<float>::Zero();
        if (enable_inertia_compensation) {
            tau_inertia = computeInertiaCompensation(q, qd, leg, data);
        }

        // 从观测扭矩中减去重力和惯性项，得到接触力产生的扭矩
        Vec3<float> tau_contact = tau_est - tau_gravity - tau_inertia;

        // 通过逆向动力学计算足端接触力
        // 公式: tau_contact = J^T * F_foot
        // 求解: F_foot = (J^T)^(-1) * tau_contact

        // 计算雅可比矩阵转置的伪逆
        Mat3<float> J_transpose = J.transpose();
        Mat3<float> J_transpose_pinv;

        // 使用SVD计算伪逆矩阵
        Eigen::JacobiSVD<Mat3<float>> svd(J_transpose, Eigen::ComputeFullU | Eigen::ComputeFullV);
        float tolerance = 1e-6f;

        // 计算伪逆
        auto singular_values = svd.singularValues();
        Mat3<float> sigma_inv = Mat3<float>::Zero();
        for (int i = 0; i < 3; i++) {
            if (singular_values(i) > tolerance) {
                sigma_inv(i, i) = 1.0f / singular_values(i);
            }
        }
        J_transpose_pinv = svd.matrixV() * sigma_inv * svd.matrixU().transpose();

        // 计算足端接触力
        // 注意：这里的符号很重要，需要根据实际的坐标系约定调整
        Vec3<float> foot_force = J_transpose_pinv * tau_contact;
        
        // 简单的低通滤波
        static Vec3<float> foot_force_tau_est_prev[4] = {Vec3<float>::Zero(), Vec3<float>::Zero(), Vec3<float>::Zero(), Vec3<float>::Zero()};
        foot_force_tau_est[leg] = torque_filter_alpha * foot_force_tau_est_prev[leg] +
                                  (1.0f - torque_filter_alpha) * foot_force;
        foot_force_tau_est_prev[leg] = foot_force_tau_est[leg];
    }
}

// computeGravityCompensation 和 computeInertiaCompensation 函数保持不变
Vec3<float> DisturbanceEstimator::computeGravityCompensation(const Vec3<float>& q, int leg, ControlFSMData<float>& data) {
    // 简化的重力补偿模型
    // 基于腿部连杆质量和重力的影响

    // 腿部连杆参数（简化模型）
    float m1 = 0.54f;  // abad连杆质量 (kg)
    float m2 = 0.634f; // hip连杆质量 (kg)
    float m3 = 0.064f; // knee连杆质量 (kg)

    float l1 = data._quadruped->_abadLinkLength;
    float l2 = data._quadruped->_hipLinkLength;
    float l3 = data._quadruped->_kneeLinkLength;

    // 重心位置（简化为连杆中点）
    float lc1 = l1 * 0.5f;
    float lc2 = l2 * 0.5f;
    float lc3 = l3 * 0.5f;

    float g = 9.81f;

    // 关节角度
    float q1 = q[0]; // abad
    float q2 = q[1]; // hip
    float q3 = q[2]; // knee

    // 计算重力扭矩（简化模型）
    Vec3<float> tau_gravity;

    // abad关节重力扭矩
    tau_gravity[0] = 0.0f; // abad轴通常垂直，重力扭矩较小

    // hip关节重力扭矩
    tau_gravity[1] = g * (m2 * lc2 * cos(q2) + m3 * (l2 * cos(q2) + lc3 * cos(q2 + q3)));

    // knee关节重力扭矩
    tau_gravity[2] = g * m3 * lc3 * cos(q2 + q3);

    return tau_gravity;
}

Vec3<float> DisturbanceEstimator::computeInertiaCompensation(const Vec3<float>& q, const Vec3<float>& qd, int leg, ControlFSMData<float>& data) {
    // 简化的惯性补偿模型
    // 基于腿部连杆惯性和科里奥利力的影响

    // 腿部连杆参数（简化模型）
    float m1 = 0.54f;  // abad连杆质量 (kg)
    float m2 = 0.634f; // hip连杆质量 (kg)
    float m3 = 0.064f; // knee连杆质量 (kg)

    float l1 = data._quadruped->_abadLinkLength;
    float l2 = data._quadruped->_hipLinkLength;
    float l3 = data._quadruped->_kneeLinkLength;

    // 惯性参数（简化）
    float I1 = m1 * l1 * l1 / 12.0f;
    float I2 = m2 * l2 * l2 / 12.0f;
    float I3 = m3 * l3 * l3 / 12.0f;

    // 关节角度和角速度
    float q1 = q[0], q2 = q[1], q3 = q[2];
    float qd1 = qd[0], qd2 = qd[1], qd3 = qd[2];

    // 简化的惯性扭矩计算（主要考虑科里奥利力和离心力）
    Vec3<float> tau_inertia;

    // 简化模型：主要考虑关节间的耦合效应
    tau_inertia[0] = I1 * 0.1f * (qd2 * qd2 + qd3 * qd3); // abad轴受其他轴影响
    tau_inertia[1] = I2 * 0.2f * qd1 * qd3 * sin(q3);     // hip轴科里奥利力
    tau_inertia[2] = I3 * 0.1f * qd1 * qd2 * cos(q2);     // knee轴科里奥利力

    return tau_inertia;
}

void DisturbanceEstimator::update(const ControlFSMData<float>& data) {
    auto& estResult = data._stateEstimator->getResult(); // 获取状态估计结果
    _SetupCommand(data);
    

}

void DisturbanceEstimator::_SetupCommand(ControlFSMData<float> & data){
  if(data._quadruped->_robotType == RobotType::MINI_CHEETAH){
    _body_height = 0.33;
  }else if(data._quadruped->_robotType == RobotType::CHEETAH_3){
    _body_height = 0.45;
  }else{
    assert(false);
  }

  float x_vel_cmd, y_vel_cmd;
  float filter(0.1);
  if(data.controlParameters->use_rc){
    const rc_control_settings* rc_cmd = data._desiredStateCommand->rcCommand;
    data.userParameters->cmpc_gait = rc_cmd->variable[0];
    if(rc_cmd->variable[3]== 0)  //0
    {}
    _yaw_turn_rate = -rc_cmd->omega_des[2];
    x_vel_cmd = rc_cmd->v_des[0];
    y_vel_cmd = rc_cmd->v_des[1] * 0.5;
    _body_height += rc_cmd->height_variation * 0.08;

  }else{
    _yaw_turn_rate = data._desiredStateCommand->rightAnalogStick[0]*1.5;
    x_vel_cmd = data._desiredStateCommand->leftAnalogStick[1]*1.5;//+0.07;
    y_vel_cmd = data._desiredStateCommand->leftAnalogStick[0];
  }
  _x_vel_des = _x_vel_des*(1-filter) + x_vel_cmd*filter;
  _y_vel_des = _y_vel_des*(1-filter) + y_vel_cmd*filter;

  _yaw_des = data._stateEstimator->getResult().rpy[2] + dt * _yaw_turn_rate;
  _roll_des = 0.;
  _pitch_des = 0.;

}