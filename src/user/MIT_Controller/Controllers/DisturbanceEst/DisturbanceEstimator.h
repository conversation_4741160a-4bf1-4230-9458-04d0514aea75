#pragma once
#include "cppTypes.h"             // 若要用 Mat3<T>/Vec3<T> 这类别名
#include <eigen3/Eigen/Dense>     // 若直接用 Eigen::Matrix
using Eigen::Matrix;
#include <FSM_States/ControlFSMData.h>
class DisturbanceEstimator {
    public:
        EIGEN_MAKE_ALIGNED_OPERATOR_NEW
        DisturbanceEstimator(float _dt, int _iterations_between_mpc, MIT_UserParameters* parameters);
        ~DisturbanceEstimator(){};
        void initialize();
        void run(ControlFSMData<float>& data);
        void update(ControlFSMData<float>& data);
        void estimateFootForceFromTorque(ControlFSMData<float>& data);
        Vec3<float> computeGravityCompensation(const Vec3<float>& q, int leg, ControlFSMData<float>& data);
        Vec3<float> computeInertiaCompensation(const Vec3<float>& q, const Vec3<float>& qd, int leg, ControlFSMData<float>& data);
        void fuseForceEstimates();
        void _SetupCommand(ControlFSMData<float>& data);

        float mp = 0.0f; //负载的估计质量
        float mp_torque_based = 0.0f; //基于扭矩估计的负载质量
        float mp_fused = 0.0f; //融合后的负载质量

        Vec4<float> contact_state; //接触状态，连续值
        Vec4<float> foot_force_est; //估计的足力 力传感器的
        Vec3<float> foot_force_tau_est[4]; //基于关节扭矩估计的足端接触力(三维)
        Vec4<float> foot_force_fused; //融合后的足端接触力估计
        float total_contact_force; //接触力之和 力传感器的
        Vec3 <float>  total_contact_force_tau_est; //基于关节扭矩估计的接触力之和
        Eigen::Matrix<float,3,3> I_body;
        float body_mass;
        Vec3<float> acc_world_3d;
        float torque_confidence = 1.0f;

        /* 扭矩和期望力的相关变量 */
        Vec3 <float> disturbance; //负载引起的扰动
        Vec3 <float> foot_force_des[4]; //期望的足力        
    private:

        float dt;
        float dtMPC;
        int iterations_between_mpc;
        MIT_UserParameters* _parameters = nullptr;
        // 成员变量建议：
        float mp_tau = 2.f;       // 低通时间常数(秒)，可调 1~5
        float mp_max = 10.f;      // 最大负载(kg)上限
        float last_contact_hys[4] = {0,0,0,0}; // 接触滞回状态(0/1)
        float F_min = 20.f;       // 单脚认为接触的最小 Fz 阈值(N)
        float total_mass_original; // 原始总质量，力传感器估计的

        // 扭矩估计相关参数
        bool enable_torque_estimation = true;  // 是否启用扭矩估计
        bool enable_gravity_compensation = false; // 是否启用重力补偿 - 暂时禁用进行调试
        bool enable_inertia_compensation = false; // 是否启用惯性补偿（可能不稳定）
        float torque_filter_alpha = 0.7f;     // 扭矩估计滤波系数
        float fusion_base_weight = 0.3f;      // 融合时扭矩估计的基础权重
        
        /* 扭矩和期望力的相关变量 */
        Vec3 <float> d_est; // 扭矩估计的负载扰动
        Vec3<float> K; ///beta的权重矩阵        
        Vec3 <float> input; // 辅助输入u；
        Vec3 <float> x_1; // theta
        Vec3 <float> x_2; // omega
        Vec3 <float> phi; // d_hat的更新率
        Vec3 <float> F_b; // 期望质心合外力
        Vec3 <float> T_b; // 期望质心扭矩

        // 设置命令
        float _body_height;
        float _yaw_turn_rate;
        float _x_vel_des;
        float _y_vel_des;
        float __yaw_des;
        float _roll_des;
        float _pitch_des;

};