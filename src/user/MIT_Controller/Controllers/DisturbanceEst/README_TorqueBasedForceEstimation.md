# 基于关节扭矩传感器的足端接触力估计实现

## 概述

本实现在MIT_Controller的DisturbanceEstimator中添加了基于关节扭矩传感器观测值的足端接触力估计功能。通过逆向动力学计算，从关节扭矩数据中估计足端接触力，并与原有的估计方法进行融合。

## 主要功能

### 1. 扭矩基础的足端力估计 (`estimateFootForceFromTorque`)

- **输入**: 关节扭矩传感器数据 `tau_est`
- **处理**: 
  - 重力补偿（可选）
  - 惯性补偿（可选）
  - 雅可比矩阵伪逆计算
  - 足端接触力解算
- **输出**: `foot_force_tau_est` - 基于扭矩的足端接触力估计

### 2. 重力补偿 (`computeGravityCompensation`)

- 基于简化的腿部连杆模型
- 考虑各连杆质量和重心位置
- 计算重力对各关节产生的扭矩

### 3. 惯性补偿 (`computeInertiaCompensation`)

- 简化的惯性模型
- 主要考虑科里奥利力和离心力
- 计算运动产生的惯性扭矩

### 4. 估计融合 (`fuseForceEstimates`)

- 将扭矩估计与原有估计进行加权融合
- 根据接触状态动态调整权重
- 包含可信度检查和异常值处理

## 核心算法

### 逆向动力学计算

```
tau_contact = tau_est - tau_gravity - tau_inertia
F_foot = (J^T)^(-1) * tau_contact
```

其中：
- `tau_est`: 关节扭矩传感器观测值
- `tau_gravity`: 重力补偿扭矩
- `tau_inertia`: 惯性补偿扭矩
- `J`: 雅可比矩阵
- `F_foot`: 足端接触力

### 融合策略

```
weight_tau = base_weight + 0.4 * contact_confidence * reliability
F_fused = weight_tau * F_tau + (1 - weight_tau) * F_original
```

## 配置参数

### 可调参数

- `enable_torque_estimation`: 是否启用扭矩估计 (默认: true)
- `enable_gravity_compensation`: 是否启用重力补偿 (默认: true)
- `enable_inertia_compensation`: 是否启用惯性补偿 (默认: false)
- `torque_filter_alpha`: 扭矩估计滤波系数 (默认: 0.7)
- `fusion_base_weight`: 融合时扭矩估计的基础权重 (默认: 0.3)

### 物理参数

- 腿部连杆质量: m1=0.54kg, m2=0.634kg, m3=0.064kg
- 最大接触力限制: 500N
- 滤波和限制阈值

## 新增成员变量

- `Vec4<float> foot_force_tau_est`: 基于关节扭矩估计的足端接触力
- `Vec4<float> foot_force_fused`: 融合后的足端接触力估计
- 各种配置参数和物理常数

## 调试输出

系统每1000次迭代输出一次调试信息，包括：
- 各腿关节扭矩观测值
- 扭矩估计的足端接触力
- 原有方法的足端接触力
- 融合后的足端接触力
- 接触状态信息

## 使用方法

1. 在`DisturbanceEstimator::run()`中自动调用
2. 可通过配置参数调整算法行为
3. 结果存储在`foot_force_tau_est`和`foot_force_fused`中
4. 可根据需要选择使用哪种估计结果

## 注意事项

1. 惯性补偿默认关闭，因为简化模型可能不够准确
2. 重力补偿使用简化的连杆模型，实际机器人参数可能需要调整
3. 融合权重可根据实际性能进行调优
4. 建议在实际使用前进行充分的测试和验证

## 未来改进方向

1. 更精确的动力学模型
2. 自适应参数调整
3. 更复杂的融合策略
4. 实时性能优化
