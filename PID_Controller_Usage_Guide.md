# PID 控制器使用指南

## 概述

在 `DisturbanceEstimator` 类中实现了姿态和位置的 PID 控制器，支持四元数姿态控制和三维位置控制。

## 功能特性

### 1. 姿态 PID 控制器
- **输入**: 当前四元数姿态、期望四元数姿态、当前角速度、期望角速度
- **输出**: 控制力矩 `attitude_control_torque`
- **误差计算**: 基于四元数的姿态误差，自动选择最短旋转路径
- **控制结构**: 双环控制（外环姿态 + 内环角速度）

### 2. 位置 PID 控制器
- **输入**: 当前位置、期望位置、当前速度、期望速度
- **输出**: 控制力 `position_control_force`
- **坐标系**: 世界坐标系
- **控制结构**: 双环控制（外环位置 + 内环速度）

## 使用方法

### 1. 初始化
```cpp
// 在 DisturbanceEstimator 构造后调用
disturbanceEstimator.initialize();
```

### 2. 设置 PID 参数
```cpp
// 姿态PID增益 [Roll, Pitch, Yaw]
Vec3<float> att_kp(50.0f, 50.0f, 20.0f);  // 比例增益
Vec3<float> att_ki(5.0f, 5.0f, 2.0f);     // 积分增益
Vec3<float> att_kd(8.0f, 8.0f, 3.0f);     // 微分增益

// 位置PID增益 [X, Y, Z]
Vec3<float> pos_kp(100.0f, 100.0f, 150.0f);  // 比例增益
Vec3<float> pos_ki(10.0f, 10.0f, 15.0f);     // 积分增益
Vec3<float> pos_kd(20.0f, 20.0f, 25.0f);     // 微分增益

disturbanceEstimator.setPIDGains(att_kp, att_ki, att_kd, pos_kp, pos_ki, pos_kd);
```

### 3. 运行控制器
```cpp
// 在控制循环中调用
disturbanceEstimator.update(data);

// 获取控制输出
Vec3<float> torque = disturbanceEstimator.attitude_control_torque;
Vec3<float> force = disturbanceEstimator.position_control_force;
```

### 4. 调试功能
```cpp
// 启用调试输出
disturbanceEstimator.enablePIDDebug(true);

// 重置积分项（在参数调整时使用）
disturbanceEstimator.resetPIDIntegrals();
```

## 控制律公式

### 姿态控制律
```
τ = Kp_att * e_att + Ki_att * ∫e_att*dt + Kd_att * de_att/dt + 
    Kp_ω * e_ω + Kd_ω * de_ω/dt
```

其中：
- `e_att`: 姿态误差（轴角表示）
- `e_ω`: 角速度误差
- `τ`: 控制力矩输出

### 位置控制律
```
F = Kp_pos * e_pos + Ki_pos * ∫e_pos*dt + Kd_pos * de_pos/dt + 
    Kp_vel * e_vel + Kd_vel * de_vel/dt
```

其中：
- `e_pos`: 位置误差
- `e_vel`: 速度误差
- `F`: 控制力输出

## 姿态误差计算

使用四元数误差计算，确保最短旋转路径：

```cpp
// 四元数误差: q_error = q_desired * q_current^(-1)
// 转换为轴角表示作为姿态误差
```

## 参数调整建议

### 姿态控制器调整
1. **比例增益 (Kp)**: 控制响应速度，过大会振荡
2. **积分增益 (Ki)**: 消除稳态误差，过大会超调
3. **微分增益 (Kd)**: 提供阻尼，减少振荡

### 位置控制器调整
1. **Z轴增益**: 通常比X、Y轴大，对抗重力
2. **积分项**: 小心使用，避免积分饱和
3. **速度反馈**: 提供必要的阻尼

## 安全特性

1. **积分限制**: 防止积分饱和
2. **使能开关**: 可独立启用/禁用姿态和位置控制
3. **调试输出**: 实时监控控制器状态

## 注意事项

1. 确保控制频率足够高（建议 > 500Hz）
2. 期望状态应平滑变化，避免阶跃输入
3. 定期检查积分项是否过大
4. 根据实际机器人参数调整增益
